{"name": "xconsole", "version": "1.0.0", "description": "", "main": "src/index.js", "scripts": {"start": "xconsole start", "build": "NODE_ENV=production xconsole build", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "lint": "eslint src/"}, "repository": {"type": "git", "url": ""}, "keywords": ["react", "redux", "dva"], "author": "", "dependencies": {"@ali/xconsole": "^2.0.0", "@alicloud/console-chart": "^0.4.9", "lodash": "^4.17.4", "moment": "^2.22.1", "prop-types": "^15.6.1", "react-hot-loader": "^4.1.2", "react-json-view": "^1.19.1", "react-markdown": "^8.0.7", "remark-gfm": "^3.0.1", "rehype-raw": "^6.1.1", "react-syntax-highlighter": "^15.5.0", "classnames": "^2.3.2", "js-yaml": "^4.1.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "eslint": "^8.5.0", "eslint-config-ali": "^14.0.2", "eslint-plugin-import": "^2.11.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.0", "typescript": "^4.9.5", "@types/js-yaml": "^4.0.5", "@types/lodash": "^4.14.195", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-syntax-highlighter": "^15.5.7", "tslib": "^2.6.0"}, "private": true}