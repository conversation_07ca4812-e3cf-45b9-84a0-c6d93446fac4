import React, { useState, useEffect } from 'react';
import { Select, Loading } from '@ali/xconsole';
import { guideEventBus } from '../../../services/GuideEventBus';

interface GuideSelectProps {
  id: string;
  label: string;
  required?: boolean;
  placeholder?: string;
  dependsOn?: string;
  dataSource?: string;
  valueField?: string;
  labelField?: string;
  disabled?: boolean;
  onEvent?: (event: any) => void;
  children?: React.ReactNode;
}

const GuideSelect: React.FC<GuideSelectProps> = ({
  id,
  label,
  required = false,
  placeholder,
  dependsOn,
  dataSource,
  valueField = 'value',
  labelField = 'label',
  disabled = false,
  onEvent,
  children
}) => {
  const [value, setValue] = useState<string>('');
  const [options, setOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // 监听依赖字段变化
  useEffect(() => {
    if (dependsOn) {
      const cleanup = guideEventBus.on('fieldChange', (data) => {
        if (data.fieldId === dependsOn) {
          setValue(''); // 清空当前值
          loadData(data.value);
        }
      });
      return cleanup;
    }
  }, [dependsOn]);

  // 初始加载数据
  useEffect(() => {
    if (dataSource && !dependsOn) {
      loadData();
    }
  }, [dataSource, dependsOn]);

  const loadData = async (dependentValue?: string) => {
    if (!dataSource) return;

    setLoading(true);
    setError('');

    try {
      let url = dataSource;
      
      // 处理API数据源
      if (dataSource.startsWith('api:')) {
        url = dataSource.replace('api:', '');
        
        // 替换依赖参数
        if (dependentValue) {
          url = url.replace(/\{\{([^}]+)\}\}/g, (match, param) => {
            return param === dependsOn ? dependentValue : match;
          });
        }

        // 模拟API调用 - 在实际项目中应该使用真实的API服务
        const mockData = await mockApiCall(url);
        setOptions(mockData);
      } else {
        // 处理静态数据
        try {
          const staticData = JSON.parse(dataSource);
          setOptions(staticData);
        } catch {
          setError('Invalid data source format');
        }
      }
    } catch (err) {
      setError('Failed to load data');
      console.error('GuideSelect data loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  // 模拟API调用
  const mockApiCall = async (url: string): Promise<any[]> => {
    // 这里应该替换为真实的API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    
    if (url.includes('/regions')) {
      return [
        { regionId: 'cn-shanghai', regionName: '华东1（上海）' },
        { regionId: 'cn-beijing', regionName: '华北2（北京）' },
        { regionId: 'cn-hangzhou', regionName: '华东1（杭州）' }
      ];
    }
    
    if (url.includes('/gateways')) {
      return [
        { gatewayId: 'gw-001', gatewayName: 'API网关实例1' },
        { gatewayId: 'gw-002', gatewayName: 'API网关实例2' }
      ];
    }
    
    return [];
  };

  const handleChange = (newValue: string) => {
    setValue(newValue);
    
    // 通知其他组件
    guideEventBus.emit('fieldChange', {
      fieldId: id,
      value: newValue
    });

    // 通知父组件
    if (onEvent) {
      onEvent({
        type: 'fieldChange',
        data: { fieldId: id, value: newValue }
      });
    }
  };

  const formattedOptions = options.map(option => ({
    value: option[valueField],
    label: option[labelField]
  }));

  return (
    <div className="guide-form-item">
      <label className="guide-form-label">
        {label}
        {required && <span className="required">*</span>}
      </label>
      <Select
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        disabled={disabled || (dependsOn && !value)}
        dataSource={formattedOptions}
        style={{ width: '100%' }}
      />
      {error && <div className="guide-form-error">{error}</div>}
      {children && <div className="guide-form-help">{children}</div>}
    </div>
  );
};

export default GuideSelect; 