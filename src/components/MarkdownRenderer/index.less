/* 引导Markdown渲染器样式 */
.guide-markdown-renderer {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;

  h1, h2, h3, h4, h5, h6 {
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
  }

  h1 {
    font-size: 2rem;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.5rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  h3 {
    font-size: 1.25rem;
  }

  p {
    margin-bottom: 1rem;
  }

  ul, ol {
    margin-bottom: 1rem;
    padding-left: 2rem;
  }

  li {
    margin-bottom: 0.25rem;
  }

  blockquote {
    margin: 1rem 0;
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border-left: 4px solid #007bff;
    color: #6c757d;
  }

  code {
    padding: 0.125rem 0.25rem;
    background: #f8f9fa;
    border-radius: 0.25rem;
    font-family: 'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, monospace;
    font-size: 0.875rem;
  }

  pre {
    margin: 1rem 0;
    border-radius: 0.375rem;
    overflow-x: auto;
  }

  pre code {
    padding: 0;
    background: transparent;
  }

  a {
    color: #007bff;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }

  img {
    max-width: 100%;
    height: auto;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
  }

  th, td {
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    text-align: left;
  }

  th {
    background: #f8f9fa;
    font-weight: 600;
  }
}

/* 引导表单组件样式 */
.guide-form-item {
  margin-bottom: 1.5rem;

  .guide-form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;

    .required {
      color: #dc3545;
      margin-left: 0.25rem;
    }
  }

  .guide-form-error {
    margin-top: 0.25rem;
    color: #dc3545;
    font-size: 0.875rem;
  }

  .guide-form-help {
    margin-top: 0.25rem;
    color: #6c757d;
    font-size: 0.875rem;
  }
}

/* 引导按钮样式 */
.guide-button {
  display: inline-block;
  padding: 0.5rem 1rem;
  margin: 0.25rem 0.5rem 0.25rem 0;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &--primary {
    background: #007bff;
    color: white;

    &:hover:not(:disabled) {
      background: #0056b3;
    }
  }

  &--secondary {
    background: #6c757d;
    color: white;

    &:hover:not(:disabled) {
      background: #545b62;
    }
  }

  &--normal {
    background: #f8f9fa;
    color: #333;
    border: 1px solid #dee2e6;

    &:hover:not(:disabled) {
      background: #e9ecef;
    }
  }

  &--highlight {
    background: linear-gradient(135deg, #1890ff, #40a9ff);
    color: white;
    border: 1px solid #1890ff;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
    position: relative;
    overflow: hidden;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #40a9ff, #69c0ff);
      box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
    }

    // 添加闪烁效果
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.6s ease;
    }

    &:hover::before {
      left: 100%;
    }
  }
}

/* 引导链接样式 */
.guide-link {
  color: #007bff;
  text-decoration: none;
  cursor: pointer;
  transition: color 0.2s ease;

  &:hover {
    color: #0056b3;
    text-decoration: underline;
  }

  &:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }
} 