import React, { useState } from 'react';
import GuidePanel from '../GuidePanel';
import { GuideConfig, Guide, GuideState } from '../../types/guide';
import { guideEventBus } from '../../services/GuideEventBus';
import { SAMPLE_GUIDE_CONFIG } from '../../constants/guide';
import './index.less';



const CardExample: React.FC = () => {
  const [state, setState] = useState<GuideState>({
    currentStepIndex: 0,
    context: {},
    isVisible: false,
  });

  const [currentGuide, setCurrentGuide] = useState<Guide | null>(null);
  const [guideConfig] = useState<GuideConfig>(SAMPLE_GUIDE_CONFIG);

  const startGuide = (guideId: string) => {
    const guide = guideConfig.guides.find(g => g.id === guideId);
    if (!guide) {
      console.warn(`Guide not found: ${guideId}`);
      return;
    }

    setCurrentGuide(guide);
    setState(prev => ({
      ...prev,
      currentGuideId: guideId,
      currentStepIndex: 0,
      isVisible: true,
    }));

    // 通知主应用
    guideEventBus.startGuide(guideId, guide.title);
  };

  return (
    <div className="quickstart-container">

      <div className="quickstart-guides">
        <h2>可用引导</h2>
        <div className="guides-grid">
          {guideConfig.guides.map(guide => (
            <div key={guide.id} className="guide-card">
              <div className="guide-card-header">
                <h3>{guide.title}</h3>
                <span className={`difficulty ${guide.difficulty}`}>
                  {guide.difficulty === 'beginner' ? '初级' : 
                   guide.difficulty === 'intermediate' ? '中级' : '高级'}
                </span>
              </div>
              <p>{guide.description}</p>
              <div className="guide-meta">
                <span>预计时间: {guide.estimatedTime}</span>
                <div className="guide-tags">
                  {guide.tags.map(tag => (
                    <span key={tag} className="tag">{tag}</span>
                  ))}
                </div>
              </div>
              <button 
                className="start-guide-btn"
                onClick={() => startGuide(guide.id)}
              >
                开始引导
              </button>
            </div>
          ))}
        </div>
      </div>

      {state.isVisible && currentGuide && (
        <GuidePanel
          guide={currentGuide}
          onComplete={() => {
            console.log('引导完成！');
            guideEventBus.endGuide(currentGuide.id, true);
          }}
          onClose={() => {
            console.log('引导关闭');
            if (currentGuide) {
              guideEventBus.endGuide(currentGuide.id, false);
            }
          }}
          onStepChange={(stepIndex) => {
            // 步骤变化时同步状态和通知主应用
            setState(prev => ({
              ...prev,
              currentStepIndex: stepIndex,
            }));
            
            const step = currentGuide.steps[stepIndex];
            if (step) {
              guideEventBus.stepChange(step.id, step.title, stepIndex);
            }
          }}
          onVisibilityChange={(visible) => {
            // 当 GuidePanel 内部状态变化时同步外部状态
            setState(prev => ({
              ...prev,
              isVisible: visible,
            }));
            
            if (!visible) {
              setCurrentGuide(null);
              guideEventBus.clearHighlight();
            }
          }}
          context={state.context}
        />
      )}

    </div>
  );
};

export default CardExample; 