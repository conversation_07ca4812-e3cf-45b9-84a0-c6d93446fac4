/* 快速入门主样式 */
@import '../MarkdownRenderer/index.less';
@import '../GuidePanel/index.less';

.quickstart-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.quickstart-header {
  text-align: center;
  margin-bottom: 3rem;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
  }

  p {
    font-size: 1.125rem;
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto;
  }
}

.quickstart-guides {
  h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1.5rem;
  }
}

.guides-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.guide-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  padding: 1.5rem;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .guide-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;

    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #333;
      margin: 0;
      flex: 1;
    }

    .difficulty {
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      font-size: 0.75rem;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      &.beginner {
        background: #d4edda;
        color: #155724;
      }

      &.intermediate {
        background: #fff3cd;
        color: #856404;
      }

      &.advanced {
        background: #f8d7da;
        color: #721c24;
      }
    }
  }

  p {
    color: #6c757d;
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }

  .guide-meta {
    margin-bottom: 1.5rem;

    > span {
      display: block;
      font-size: 0.875rem;
      color: #6c757d;
      margin-bottom: 0.5rem;
    }

    .guide-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;

      .tag {
        padding: 0.25rem 0.5rem;
        background: #f8f9fa;
        color: #6c757d;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
      }
    }
  }

  .start-guide-btn {
    width: 100%;
    padding: 0.75rem;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background: #0056b3;
    }

    &:active {
      transform: translateY(1px);
    }
  }
}

/* 调试面板样式 */
.debug-panel {
  position: fixed;
  bottom: 1rem;
  left: 1rem;
  background: #333;
  color: white;
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 10000;
  min-width: 200px;

  h4 {
    margin: 0 0 0.75rem 0;
    font-size: 0.875rem;
    color: #ccc;
  }

  button {
    display: block;
    width: 100%;
    margin-bottom: 0.5rem;
    padding: 0.375rem 0.75rem;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    cursor: pointer;

    &:hover {
      background: #0056b3;
    }

    &:last-of-type {
      background: #dc3545;

      &:hover {
        background: #c82333;
      }
    }
  }

  div {
    font-size: 0.75rem;
    color: #ccc;
    margin-bottom: 0.25rem;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .quickstart-container {
    padding: 1rem;
  }

  .quickstart-header {
    margin-bottom: 2rem;

    h1 {
      font-size: 2rem;
    }

    p {
      font-size: 1rem;
    }
  }

  .guides-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .guide-card {
    padding: 1rem;
  }

  .debug-panel {
    bottom: 0.5rem;
    left: 0.5rem;
    right: 0.5rem;
    min-width: auto;
  }
} 