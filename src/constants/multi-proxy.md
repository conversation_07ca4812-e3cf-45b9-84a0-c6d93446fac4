<!-- # 场景描述
AI网关能够将外部调用不同大模型的请求，通过统一的调用方式转发到内部对应的大模型上，使得后端模型调度更加灵活；Higress AI网关支持常用的100+个模型的统一协议转换，并支持模型级Fallback。

在大模型评测过程中，多模型代理功能可以构造统一数据集，将模型请求转发到后端模型，验证模型的效果；结合可观测插件，能够清晰地追踪不同模型的链路。 -->

# 选择网关实例
1111
<CustomComponent id="regionId" />
2222
<guide-select id="gateway" />

# 配置大模型服务

进入网关详情后，进入 [服务](goToUrl "/\${regionId}/ai-gateway/\${id}/service") 菜单。点击[创建服务](highlight_text "创建服务")按钮。AI服务提供者管理界面，可以配置已集成供应商的API-KEY。当前已集成的供应商有阿里云、DeepSeek、Azure OpenAI、OpenAI、豆包等。，配置完成后点击[确定](highlight_text "确定")按钮。

# 配置 Model API
进入[Model API](goToUrl "/\${regionId}/ai-gateway/\${id}/model-api") 菜单，点击 [创建Model API](highlight_text "创建Model API") 按钮，创建[文本生成](highlight_text "文本生成") API，配置基本信息后，服务列表选择刚创建的大模型服务，打开[Fallback](highlight_text "Fallback")开关，配置Fallback模型，点击[确定](highlight_text "确定")按钮。

# 调试
对刚刚配置的Model API进行调试，点击[调试](highlight_text "调试")按钮，填写请求参数，点击[发送](highlight_text "发送")按钮，查看返回结果。

# 结果观测
进入Model API详情页面，点击[统计](highlight_text "统计")，可以在AI监控面板界面，可以对AI请求进行观测。观测指标包括每秒输入输出Token数量、各供应商/模型Token使用数量等。