import { GuideTarget, HighlightOptions } from './GuideEventBus';

class HighlightService {
  private currentHighlight: HTMLElement | null = null;

  highlightElement(target: GuideTarget, options: HighlightOptions = {}) {
    this.clearHighlight();
    
    const element = this.findElement(target);
    if (!element) {
      console.warn('❌ 未找到目标元素:', target);
      return;
    }

    const defaultOptions = {
      padding: 8,
      borderRadius: 4,
      borderColor: '#1890ff',
      borderWidth: 2,
      animation: 'pulse',
      ...options
    };

    const rect = element.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
    
    // 创建高亮遮罩
    const highlight = document.createElement('div');
    highlight.id = 'guide-highlight-overlay';
    highlight.style.cssText = `
      position: absolute;
      top: ${rect.top + scrollTop - defaultOptions.padding}px;
      left: ${rect.left + scrollLeft - defaultOptions.padding}px;
      width: ${rect.width + defaultOptions.padding * 2}px;
      height: ${rect.height + defaultOptions.padding * 2}px;
      border: ${defaultOptions.borderWidth}px solid ${defaultOptions.borderColor};
      border-radius: ${defaultOptions.borderRadius}px;
      pointer-events: none;
      z-index: 999999;
      box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.3);
      animation: ${defaultOptions.animation === 'pulse' ? 'guide-pulse 2s infinite' : 'none'};
    `;
    
    this.addStyles();
    document.body.appendChild(highlight);
    this.currentHighlight = highlight;

    // 滚动到目标元素
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }

  clearHighlight() {
    if (this.currentHighlight) {
      this.currentHighlight.remove();
      this.currentHighlight = null;
    }
  }

  private findElement(target: GuideTarget): Element | null {
    // 策略1: CSS选择器
    if (target.selector) {
      return document.querySelector(target.selector);
    }
    
    // 策略2: data-guide-id
    if (target.guideId) {
      return document.querySelector(`[data-guide-id="${target.guideId}"]`);
    }
    
    // 策略3: 文本匹配
    if (target.text) {
      const elements = Array.from(document.querySelectorAll('button, a, [role="button"]'))
        .filter(el => el.textContent?.trim().includes(target.text));
      return elements[0] || null;
    }
    
    return null;
  }

  private addStyles() {
    if (!document.getElementById('guide-highlight-styles')) {
      const style = document.createElement('style');
      style.id = 'guide-highlight-styles';
      style.textContent = `
        @keyframes guide-pulse {
          0% { opacity: 1; transform: scale(1); }
          50% { opacity: 0.8; transform: scale(1.02); }
          100% { opacity: 1; transform: scale(1); }
        }
      `;
      document.head.appendChild(style);
    }
  }
}

export default HighlightService;