// 引导事件总线
class GuideEventBus {
  private listeners: Map<string, Function[]> = new Map();
  private mainEventListeners: Map<string, Function[]> = new Map();

  constructor() {
    // 监听来自主应用的消息
    if (typeof window !== 'undefined') {
      window.addEventListener('message', this.handleMainAppMessage.bind(this));
    }
  }

  private handleMainAppMessage(event: MessageEvent) {
    const { type, data } = event.data;
    
    switch (type) {
      case 'HIGHLIGHT_RESULT':
        if (data.success) {
          console.log('✅ 主应用高亮成功');
        } else {
          console.warn('❌ 主应用高亮失败，使用降级方案');
          this.basicHighlight(data.target, data.options);
        }
        break;
        
      case 'CLEAR_HIGHLIGHT_RESULT':
        console.log('🧹 主应用清除高亮完成');
        break;
    }
  }

  // ============ 基础事件系统 ============
  
  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
    
    return () => {
      const callbacks = this.listeners.get(event);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
      }
      }
    };
  }

  emit(event: string, data?: any) {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
  }
  }

  // ============ 主应用事件通信 ============
  
  onMainEvent(event: string, callback: Function) {
    if (!this.mainEventListeners.has(event)) {
      this.mainEventListeners.set(event, []);
    }
    this.mainEventListeners.get(event)!.push(callback);

    return () => {
      const callbacks = this.mainEventListeners.get(event);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
  }
      }
    };
  }

  // ============ 引导生命周期事件 ============
  
  startGuide(guideId: string, title: string) {
    console.log(`🚀 引导开始: ${title} (${guideId})`);
    this.emit('guide:start', { guideId, title });
    
    // 通知主应用
    this.postMessageToParent({
      type: 'GUIDE_START',
      guideId,
      title
    });
  }

  endGuide(guideId: string, completed: boolean) {
    console.log(`🏁 引导${completed ? '完成' : '退出'}: ${guideId}`);
    this.emit('guide:end', { guideId, completed });
    
    // 清除高亮
    this.clearHighlight();
    
    // 通知主应用
    this.postMessageToParent({
      type: 'GUIDE_END',
      guideId,
      completed
    });
  }

  stepChange(stepId: string, title: string, stepIndex: number) {
    console.log(`📍 步骤变化: ${title} (${stepIndex + 1})`);
    this.emit('guide:stepChange', { stepId, title, stepIndex });
    
    // 通知主应用
    this.postMessageToParent({
      type: 'GUIDE_STEP_CHANGE',
      stepId,
      title,
      stepIndex
    });
  }

  // ============ 智能高亮系统 ============

  /**
   * 高亮目标元素
   * @param target 目标配置
   * @param options 高亮选项
   */
  highlight(target: string | GuideTarget, options: HighlightOptions = {}) {
    const targetConfig = this.normalizeTarget(target);

    console.log('🎯 请求高亮:', targetConfig);

    // 优先尝试同域直接操作
    if (window === window.parent) {
      this.directHighlight(targetConfig, options);
    } else {
      // 跨域时使用 postMessage
      this.postMessageToParent({
        type: 'GUIDE_HIGHLIGHT',
        target: targetConfig,
        options
      });
    }

    this.emit('guide:highlight', { target: targetConfig, options });
  }

  /**
   * 清除高亮
   */
  clearHighlight() {
    console.log('🧹 清除高亮');
    
    // 优先尝试同域直接操作
    if (window === window.parent) {
      this.directClearHighlight();
    } else {
      // 跨域时使用 postMessage
      this.postMessageToParent({
        type: 'GUIDE_CLEAR_HIGHLIGHT'
      });
    }
    
    this.emit('guide:clearHighlight');
  }

  /**
   * 标准化目标配置
   */
  private normalizeTarget(target: string | GuideTarget): GuideTarget {
    if (typeof target === 'string') {
      // 智能判断字符串类型
      if (target.startsWith('[') || target.startsWith('#') || target.startsWith('.')) {
        // CSS 选择器
        return { selector: target };
      } else if (target.includes('-') && !target.includes(' ')) {
        // 可能是 guide-id
        return { guideId: target };
      } else {
        // 文本内容
        return { text: target };
      }
    }
    
    return target;
  }

  /**
   * 同域直接高亮
   */
  private directHighlight(target: GuideTarget, options: HighlightOptions) {
    // 检查是否有全局高亮服务
    if (window.guideHighlightService) {
      window.guideHighlightService.highlightElement(target, options);
    } else {
      // 降级到基础高亮
      this.basicHighlight(target, options);
    }
  }

  /**
   * 同域直接清除高亮
   */
  private directClearHighlight() {
    if (window.guideHighlightService) {
      window.guideHighlightService.clearHighlight();
    } else {
      // 降级清除
      const existing = document.getElementById('guide-highlight-overlay');
      if (existing) {
        existing.remove();
      }
    }
  }

  /**
   * 基础高亮实现（降级方案）
   */
  private basicHighlight(target: GuideTarget, options: HighlightOptions) {
    const element = this.findElementBasic(target);
    if (!element) {
      console.warn('❌ 未找到目标元素:', target);
      return;
        }

    const defaultOptions = {
      padding: 8,
      borderRadius: 4,
      borderColor: '#1890ff',
      borderWidth: 2,
      ...options
    };

    const rect = element.getBoundingClientRect();
    
    // 清除已存在的高亮
    this.directClearHighlight();
    
    // 创建简单边框高亮
    const highlight = document.createElement('div');
    highlight.id = 'guide-highlight-overlay';
    highlight.style.cssText = `
      position: fixed;
      top: ${rect.top - defaultOptions.padding}px;
      left: ${rect.left - defaultOptions.padding}px;
      width: ${rect.width + defaultOptions.padding * 2}px;
      height: ${rect.height + defaultOptions.padding * 2}px;
      border: ${defaultOptions.borderWidth}px solid ${defaultOptions.borderColor};
      border-radius: ${defaultOptions.borderRadius}px;
      pointer-events: none;
      z-index: 999999;
      animation: guide-basic-pulse 2s infinite;
    `;
    
    // 添加基础动画
    if (!document.getElementById('guide-basic-styles')) {
      const style = document.createElement('style');
      style.id = 'guide-basic-styles';
      style.textContent = `
        @keyframes guide-basic-pulse {
          0% { opacity: 1; transform: scale(1); }
          50% { opacity: 0.7; transform: scale(1.02); }
          100% { opacity: 1; transform: scale(1); }
        }
      `;
      document.head.appendChild(style);
    }
    
    document.body.appendChild(highlight);
  }

  /**
   * 基础元素查找
   */
  private findElementBasic(target: GuideTarget): Element | null {
    // 策略1: CSS选择器
    if (target.selector) {
      const el = document.querySelector(target.selector);
      if (el) return el;
    }
    
    // 策略2: data-guide-id
    if (target.guideId) {
      const el = document.querySelector(`[data-guide-id="${target.guideId}"]`);
      if (el) return el;
    }
    
    // 策略3: 文本匹配
    if (target.text) {
      const elements = Array.from(document.querySelectorAll('*'))
        .filter(el => el.children.length === 0 || el.tagName === 'BUTTON' || el.tagName === 'A')
        .filter(el => el.textContent?.trim().includes(target.text));
      
      return elements[0] || null;
    }
    
    return null;
  }

  /**
   * 向父窗口发送消息
   */
  private postMessageToParent(message: any) {
    if (window !== window.parent) {
      window.parent.postMessage(message, '*');
  }
  }

  // ============ 导航事件 ============
  
  navigate(url: string, openInNewTab = false) {
    console.log(`🔗 导航到: ${url}`);
    this.emit('guide:navigate', { url, openInNewTab });
    
    if (openInNewTab) {
      window.open(url, '_blank');
    } else {
      // 通知主应用处理导航
      this.postMessageToParent({
        type: 'GUIDE_NAVIGATE',
        url,
        openInNewTab
      });
    }
  }

  // ============ 组件事件 ============
  
  componentEvent(event: GuideEvent) {
    console.log('🔧 组件事件:', event);
    this.emit('guide:componentEvent', event);
    
    // 通知主应用
    this.postMessageToParent({
      type: 'GUIDE_COMPONENT_EVENT',
      event
    });
  }
  }

// ============ 类型定义 ============

export interface GuideTarget {
  /** CSS选择器 */
  selector?: string;
  /** Guide ID */
  guideId?: string;
  /** 文本内容 */
  text?: string;
  /** 查找选项 */
  options?: {
    fuzzy?: boolean;
    tagFilter?: string[];
  };
}

export interface HighlightOptions {
  /** 内边距 */
  padding?: number;
  /** 边框圆角 */
  borderRadius?: number;
  /** 动画类型 */
  animation?: 'pulse' | 'none';
  /** 背景色 */
  backgroundColor?: string;
  /** 边框颜色 */
  borderColor?: string;
  /** 边框宽度 */
  borderWidth?: number;
}

export interface GuideEvent {
  type: string;
  data?: any;
}

// 全局类型扩展
declare global {
  interface Window {
    guideHighlightService?: {
      highlightElement(target: GuideTarget, options?: HighlightOptions): void;
      clearHighlight(): void;
      currentHighlight?: HTMLElement | null;
      findElement: any;
    };
  }
}

// 导出单例
export const guideEventBus = new GuideEventBus(); 
